# Environment Configuration
NODE_ENV=development

# Server Configuration
SERVER_WALLET_PORT=3000
SERVER_LAUNCHER_PORT=3001
SERVER_MOCK_PORT=3003
INTERNAL_SERVER_PORT=4054

# OPERATOR API Configuration
OPERATOR_BASE_ENGINE_URL=http://casinoengine.test.pronetgaming.com

# Logging Configuration
LOG_LEVEL=info
LOGGING_OUTPUT_TYPE=console
LOGGING_ROOT_LOGGER=sw-integration-api

# Merchant Configuration
MERCHANT_TYPE=pronet
DEFAULT_JURISDICTION=UK

# Currency Configuration
CURRENCY_UNIT_MULTIPLIER=100

# Internal API
INTERNAL_API=false

# Measures Provider
MEASURES_PROVIDER=prometheus
