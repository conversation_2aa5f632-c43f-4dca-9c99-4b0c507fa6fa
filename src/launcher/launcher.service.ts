import config from "@config";
import { OperatorGameLaunchRequest, IntegrationInitRequest } from "@entities/operator.entities";
import { Names } from "@names";
import { Inject, Injectable } from "@nestjs/common";
import { measures } from "@skywind-group/sw-utils";
import { BaseHttpService, PlayMode } from "@skywind-group/sw-wallet-adapter-core";

@Injectable()
export class LauncherService {
    public static FALCON_GAME_URL = "v1/merchants/game/url";

    constructor(@Inject(Names.BaseHttpService) private readonly baseHttpService: BaseHttpService) { }

    @measures.measure({ name: "LauncherService.getGameUrl", isAsync: true })
    async getGameUrl(data: OperatorGameLaunchRequest, ip: string) {
        const { url } = await this.baseHttpService.post<{ url: string }>(LauncherService.FALCON_GAME_URL, this.mapToSW(data, ip));
        return { url };
    }

    private mapToSW(req: OperatorGameLaunchRequest, ip: string): IntegrationInitRequest {
        const merchantType = req.merchantType || config.merchantType;
        return {
            merchantType: config.merchantType,
            merchantCode: req.merchantCode || `${merchantType}_${req.trader}__${config.jurisdictionCode}`,
            gameCode: req.gameId,
            playMode: req.demo ? PlayMode.FUN : PlayMode.REAL,
            language: req.lang,
            lobby: req.lobby,
            ip,
            ticket: req.token,

            // operator-specific data
            customer: req.customer,
            currency: req.currency,
            country: req.country,
            platform: req.platform === "d" ? "desktop" : "mobile",
        };
    }
}
