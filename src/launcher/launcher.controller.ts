import { OperatorGameLaunchRequest } from "@entities/operator.entities";
import { Controller, Get, Query, Redirect, UseFilters, ValidationPipe } from "@nestjs/common";
import { ClientIp, ErrorFilter } from "@skywind-group/sw-integration-core";
import { LauncherService } from "./launcher.service";

@UseFilters(ErrorFilter)
@Controller("game")
export class LauncherController {
    constructor(private readonly launcherService: LauncherService) { }

    @Get("/url")
    @Redirect()
    getGameUrl(@Query(new ValidationPipe({ transform: true })) data: OperatorGameLaunchRequest, @ClientIp() ip: string): Promise<{ url: string }> {
        return this.launcherService.getGameUrl(data, ip);
    }
}
