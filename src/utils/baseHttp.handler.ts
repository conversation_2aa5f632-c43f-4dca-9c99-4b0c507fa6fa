import config from "@config";
import { OperatorBaseResponse } from "@entities/operator.entities";
import { mapOperatorToSWError } from "@errors/operator.errors";
import { HTTPOperatorRequest } from "@skywind-group/sw-integration-core";
import { MerchantInfo } from "@skywind-group/sw-wallet-adapter-core";
import * as CryptoJS from "crypto-js";
import * as superagent from "superagent";

export interface HttpHandlerRequest<T = any> {
    payload: T;
    retryAvailable?: boolean;
}

export abstract class BaseHttpHandler {
    protected buildHttpRequest<T = any>(uri: string, merchantInfo: MerchantInfo, { payload, retryAvailable }: HttpHandlerRequest<T>): HTTPOperatorRequest<T> {
        const { serverUrl, vendorCode, genericId, genericSecretKey } = merchantInfo.params;
        return {
            method: "post",
            headers: {
                "Content-Type": "application/json",
                "Generic-Id": genericId,
                "Hash": CryptoJS.SHA256(JSON.stringify(payload) + genericSecretKey).toString()
            },
            baseUrl: `${serverUrl}/casino-engine/generic/${vendorCode}/v5`,
            uri,
            payload,
            timeout: config.http.defaultOptions.timeout,
            proxy: config.http.defaultOptions.proxy,
            ssl: config.http.ssl,
            retryAvailable: retryAvailable !== false
        };
    }

    protected parseHttpResponse<T extends OperatorBaseResponse>(response: superagent.Response): T {
        const body = response.body as T;
        if (body.code === 0 && body.status.toUpperCase() === "SUCCESS") {
            return body;
        }
        throw mapOperatorToSWError({
            code: body.code || -1,
            status: body.status || "Unknown error"
        });
    }

    // operator expects amounts in the currency's minor units (e.g., cents for USD)
    protected sanitizeAmount(amount: number): number {
        return Math.round(amount * 100) / 100;
    }

    protected generateTransactionId(prefix: string = "trx"): string {
        return `${prefix}_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    }

    protected generateBetId(roundId: string): string {
        return `bet_${roundId}_${Date.now()}`;
    }

    protected buildFreespinInfo(freeSpinData?: any): any {
        if (!freeSpinData) {
            return undefined;
        }
        return {
            freespinRef: freeSpinData.freespinRef,
            requested: freeSpinData.requested || false,
            remainingRounds: freeSpinData.remainingRounds,
            totalWinnings: freeSpinData.totalWinnings
        };
    }
}

export const successResponses = [200, 201, 202];
