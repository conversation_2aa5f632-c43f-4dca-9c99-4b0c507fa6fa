// this should be the first line
import { measures } from "@skywind-group/sw-utils";
// this should be the second line
measures.measureProvider.baseInstrument();
import "module-alias/register";
import { bootstrapServer } from "@skywind-group/sw-integration-core";
import config from "@config";
import { LauncherModule } from "@launcher/launcher.module";

bootstrapServer({
    serviceName: "sw-pronet-launcher",
    versionFile: "./out/version",
    module: LauncherModule,
    internalPort: config.internalServer.port,
    port: config.server.launcherPort,
    secureKeys: config.securedKeys
});
