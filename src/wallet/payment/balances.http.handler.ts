import { IntegrationGameTokenData, OperatorAuthRequest, OperatorAuthResponse } from "@entities/operator.entities";
import { Injectable } from "@nestjs/common";
import { BalanceRequest, HttpHandler, HTTPOperatorRequest } from "@skywind-group/sw-integration-core";
import { Balances } from "@skywind-group/sw-wallet-adapter-core";
import { BaseHttpHandler } from "@utils/baseHttp.handler";
import * as superagent from "superagent";

export type IntegrationGetBalancesRequest = BalanceRequest<IntegrationGameTokenData>;

@Injectable()
export class BalancesHttpHandler extends BaseHttpHandler implements <PERSON>ttpHandler<IntegrationGetBalancesRequest, Balances> {

    public async build({ gameTokenData: { playerCode: customer, token }, merchantInfo }: IntegrationGetBalancesRequest): Promise<HTTPOperatorRequest> {
        return this.buildHttpRequest<OperatorAuthRequest>("auth", merchantInfo, {
            payload: { customer, token },
            retryAvailable: true
        });
    }

    public async parse(response: superagent.Response): Promise<Balances> {
        const { balance, bonusBalance, currency } = this.parseHttpResponse<OperatorAuthResponse>(response);
        return {
            [currency]: {
                main: this.sanitizeAmount(balance + (bonusBalance || 0))
            }
        };
    }
}
