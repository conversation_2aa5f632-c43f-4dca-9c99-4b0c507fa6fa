import { IntegrationGameTokenData, IntegrationPaymentRequest, OperatorPromoType } from "@entities/operator.entities";
import {
    OperatorBetAlreadySettledError,
    refundCondition,
    rollbackCondition
} from "@errors/operator.errors";
import { Injectable } from "@nestjs/common";
import {
    BalanceRequest,
    BalanceSupport,
    BasePaymentRequest,
    BonusPaymentSupport,
    HttpGateway,
    JackpotPaymentSupport,
    SplitPaymentSupport
} from "@skywind-group/sw-integration-core";
import { logging, measures } from "@skywind-group/sw-utils";
import { Balance, Balances, RequireRefundBetError } from "@skywind-group/sw-wallet-adapter-core";
import { BalanceHttpHandler, IntegrationGetBalanceRequest } from "@wallet/payment/balance.http.handler";
import { BalancesHttpHandler, IntegrationGetBalancesRequest } from "@wallet/payment/balances.http.handler";
import { BetHttpHandler } from "@wallet/payment/bet.http.handler";
import { DebitCreditHttpHandler } from "@wallet/payment/debitCredit.http.handler";
import { PromoHttpHandler } from "@wallet/payment/promo.http.handler";
import { RollbackHttpHandler } from "@wallet/payment/rollback.http.handler";
import { WinHttpHandler } from "@wallet/payment/win.http.handler";

const log = logging.logger("PaymentService");

@Injectable()
export class PaymentService implements
    BalanceSupport<IntegrationGameTokenData>,
    SplitPaymentSupport,
    JackpotPaymentSupport,
    BonusPaymentSupport {

    constructor(
        private readonly httpGateway: HttpGateway,
        private readonly betHandler: BetHttpHandler,
        private readonly winHandler: WinHttpHandler,
        private readonly balanceHandler: BalanceHttpHandler,
        private readonly balancesHandler: BalancesHttpHandler,
        private readonly debitCreditHandler: DebitCreditHttpHandler,
        private readonly rollbackHandler: RollbackHttpHandler,
        private readonly promoHandler: PromoHttpHandler
    ) { }

    @measures.measure({ name: "PaymentService.commitBetPayment", isAsync: true })
    public async commitBetPayment(req: IntegrationPaymentRequest): Promise<Balance> {
        try {
            if (!req.request.bet || req.request.bet === 0) {
                log.info("Fetch balance instead of zero-bet");
                return await this.getBalance(req);
            }
            // For pronet, we can use either separate debit or combined debit-credit
            // Use debit-credit if we have both bet and win in the same request
            if (req.request.totalWin && req.request.totalWin > 0) {
                log.info("Using debit-credit for combined bet and win");
                return await this.httpGateway.request<IntegrationPaymentRequest, Balance>(req, this.debitCreditHandler);
            }
            return await this.httpGateway.request<IntegrationPaymentRequest, Balance>(req, this.betHandler);
        } catch (err) {
            if (refundCondition(err)) {
                log.info(err, "Refund bet");
                throw new RequireRefundBetError();
            }
            if (rollbackCondition(err)) {
                log.info(err, "Rollback bet");
                await this.rollbackTransaction(req);
            }
            log.info(err, "Bet payment failed");
            throw err;
        }
    }

    @measures.measure({ name: "PaymentService.commitWinPayment", isAsync: true })
    public async commitWinPayment(req: IntegrationPaymentRequest): Promise<Balance> {
        if (req.request.totalWin > 0) {
            try {
                return await this.httpGateway.request<IntegrationPaymentRequest, Balance>(req, this.winHandler);
            } catch (err) {
                if (err instanceof OperatorBetAlreadySettledError && req.request.retry) {
                    log.warn(err, "Bet is already settled. Fetch balance instead");
                    return this.getBalance(req);
                }
                throw err;
            }
        }
        log.info("Fetch balance instead of zero-win");
        return this.getBalance(req);
    }

    @measures.measure({ name: "PaymentService.commitJackpotWinPayment", isAsync: true })
    public commitJackpotWinPayment(req: IntegrationPaymentRequest): Promise<Balance> {
        // For jackpot wins, use the promo handler with JPW type
        const promoRequest = {
            ...req,
            promoType: OperatorPromoType.JPW,
            promoRef: req.request.transactionId.publicId
        };
        return this.httpGateway.request<IntegrationPaymentRequest, Balance>(promoRequest, this.promoHandler);
    }

    @measures.measure({ name: "PaymentService.commitBonusPayment", isAsync: true })
    public async commitBonusPayment(req: IntegrationPaymentRequest): Promise<Balance> {
        // For bonus payments, determine the promo type based on the promo type
        const promoType = this.mapBonusToPromoType(req.request.promoType);
        const promoRequest = {
            ...req,
            promoType,
            promoRef: req.request.transactionId.publicId
        };
        return this.httpGateway.request<IntegrationPaymentRequest, Balance>(promoRequest, this.promoHandler);
    }

    @measures.measure({ name: "PaymentService.getBalances", isAsync: true })
    public async getBalances(req: IntegrationGetBalancesRequest): Promise<Balances> {
        return this.httpGateway.request<IntegrationGetBalancesRequest, Balances>(req, this.balancesHandler);
    }

    @measures.measure({ name: "PaymentService.rollbackTransaction", isAsync: true })
    public async rollbackTransaction(req: IntegrationPaymentRequest): Promise<Balance> {
        try {
            return await this.httpGateway.request<IntegrationPaymentRequest, Balance>(req, this.rollbackHandler);
        } catch (err) {
            log.error(err, "Failed to rollback transaction");
            // Return current balance if rollback fails
            return this.getBalance(req);
        }
    }

    private getBalance(req: BasePaymentRequest<IntegrationGameTokenData>): Promise<Balance> {
        return this.httpGateway.request<IntegrationGetBalanceRequest, Balance>(req, this.balanceHandler);
    }

    private mapBonusToPromoType(bonusType?: string): OperatorPromoType {
        switch (bonusType?.toLowerCase()) {
            case "freespin":
            case "freespins":
                return OperatorPromoType.FSW;
            case "cashback":
                return OperatorPromoType.CB;
            case "tournament":
                return OperatorPromoType.TW;
            case "reward":
                return OperatorPromoType.RW;
            case "rakeback":
                return OperatorPromoType.RB;
            default:
                return OperatorPromoType.RW; // Default to reward
        }
    }
}
