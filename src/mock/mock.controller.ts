import {
    OperatorCreditRequest,
    OperatorDebitCreditRequest,
    OperatorDebitRequest,
    OperatorRollbackRequest
} from "@entities/operator.entities";
import { Body, Controller, HttpCode, Post, UseGuards, UseInterceptors } from "@nestjs/common";
import { mock } from "@skywind-group/sw-integration-core";
import { IdentityGuard } from "./identity.guard";
import { MockService } from "./mock.service";

@UseGuards(IdentityGuard)
@Controller("casino-engine/generic/:vendorCode/v5")
@UseInterceptors(mock.CustomErrorInterceptor, mock.ExtraDataInterceptor)
export class MockController {

    constructor(private readonly service: MockService) { }

    @Post("auth")
    @HttpCode(200)
    public auth(@mock.MerchantParam() merchant: mock.Merchant, @mock.CustomerParam() customer: mock.Customer) {
        return this.service.getBalance(merchant, customer);
    }

    @Post("debit")
    @HttpCode(200)
    public debit(@mock.MerchantParam() merchant: mock.Merchant, @mock.CustomerParam() customer: mock.Customer, @Body() body: OperatorDebitRequest) {
        return this.service.debit(merchant, customer, body);
    }

    @Post("credit")
    @HttpCode(200)
    public credit(@mock.MerchantParam() merchant: mock.Merchant, @mock.CustomerParam() customer: mock.Customer, @Body() body: OperatorCreditRequest) {
        return this.service.credit(merchant, customer, body);
    }

    @Post("debit-credit")
    @HttpCode(200)
    public debitCredit(@mock.MerchantParam() merchant: mock.Merchant, @mock.CustomerParam() customer: mock.Customer, @Body() body: OperatorDebitCreditRequest) {
        return this.service.debitCredit(merchant, customer, body);
    }

    @Post("rollback")
    @HttpCode(200)
    public rollback(@mock.MerchantParam() merchant: mock.Merchant, @mock.CustomerParam() customer: mock.Customer, @Body() body: OperatorRollbackRequest) {
        return this.service.rollback(merchant, customer, body);
    }

    @Post("promo")
    @HttpCode(200)
    public promo(@mock.CustomerParam() customer: mock.Customer) {
        return this.service.promo(customer);
    }
}
