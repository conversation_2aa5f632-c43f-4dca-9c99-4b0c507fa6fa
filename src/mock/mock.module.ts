import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { mock } from "@skywind-group/sw-integration-core";
import { IdentityGuard } from "./identity.guard";
import { MockController } from "./mock.controller";
import { MockService } from "./mock.service";
import { AuthResponder } from "./responders/auth.responder";
import { DebitCreditResponder } from "./responders/debit-credit.responder";
import { PaymentResponder } from "./responders/payment.responder";

@Module({
    controllers: [MockController],
    providers: [
        MockService,
        IdentityGuard,
        AuthResponder,
        PaymentResponder,
        DebitCreditResponder
    ],
    imports: [mock.MockModule]
})
export class MockModule {
}
