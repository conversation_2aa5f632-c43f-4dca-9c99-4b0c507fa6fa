// this should be the first line
import { measures } from "@skywind-group/sw-utils";
// this should be the second line
measures.measureProvider.baseInstrument();
import "module-alias/register";
import { bootstrapServer } from "@skywind-group/sw-integration-core";
import { WalletModule } from "@wallet/wallet.module";
import config from "@config";

bootstrapServer({
    serviceName: "sw-pronet-wallet",
    versionFile: "./out/version",
    module: WalletModule,
    internalPort: config.internalServer.port,
    port: config.server.walletPort,
    secureKeys: config.securedKeys
});
