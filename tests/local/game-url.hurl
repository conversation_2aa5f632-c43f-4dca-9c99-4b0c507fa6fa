POST http://localhost:6003/v1/merchant
Content-Type: application/json
{
  "merch_id": "pronet_mock_1"
}

POST http://localhost:6003/v1/merchant/pronet_mock_1/customer
Content-Type: application/json
{
  "cust_id": "Customer123",
  "cust_login": "PLAYER1",
  "currency_code": "USD"
}

POST http://localhost:6003/v1/merchant/pronet_mock_1/customer/Customer123/balance/2000
accept: application/json
Content-Type: application/x-www-form-urlencoded

GET http://localhost:6003/v1/merchant/pronet_mock_1/customer/Customer123/ticket
HTTP 200
[Captures]
ticket: body

#
POST http://localhost:6000/games/test_game/url
Content-Type: application/json
{
  "merchantInfo": {
    "type": "pronet",
    "code": "pronet_mock_1",
    "brandId": 1,
    "params": {
      "serverUrl": "http://localhost:6003",
      "vendorCode": "pronet_mock_1",
      "genericId": "pronet_mock_1",
      "genericSecretKey": "pronet_mock_1"
    }
  },
  "providerCode": "skywind",
  "providerGameCode": "test_game",
  "initRequest": {
    "merchantType": "pronet",
    "merchantCode": "pronet_mock_1",
    "gameCode": "test_game",
    "playMode": "real",
    "language": "en",
    "ticket": "{{ticket}}",
    "customer": "Customer123",
    "currency": "USD",
    "country": "US",
    "platform": "desktop"
  }
}
