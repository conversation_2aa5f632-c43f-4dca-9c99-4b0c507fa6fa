POST http://localhost:6003/v1/merchant
Content-Type: application/json
{
  "merch_id": "pronet_mock_1"
}

POST http://localhost:6003/v1/merchant/pronet_mock_1/customer
Content-Type: application/json
{
  "cust_id": "Customer123",
  "cust_login": "PLAYER1",
  "currency_code": "USD"
}

POST http://localhost:6003/v1/merchant/pronet_mock_1/customer/Customer123/balance/2000
accept: application/json
Content-Type: application/x-www-form-urlencoded

#
GET http://localhost:6003/v1/merchant/pronet_mock_1/customer/Customer123/ticket
HTTP 200
[Captures]
ticket: body

# Get Balances
POST http://localhost:6000/balances
Content-Type: application/json
{
  "merchantInfo": {
    "code": "pronet_mock_1",
    "params": {
      "serverUrl": "http://localhost:6003",
      "vendorCode": "pronet_mock_1",
      "genericId": "pronet_mock_1",
      "genericSecretKey": "pronet_mock_1"
    }
  },
  "gameTokenData": {  
    "playerCode": "Customer123",
    "token": "{{ticket}}"
  }
}
