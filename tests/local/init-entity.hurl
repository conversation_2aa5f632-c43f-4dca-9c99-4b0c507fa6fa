# Login
POST http://localhost:3000/v1/login
Content-Type: application/json
{
  "username": "SUPERADMIN",
  "secretKey": "{{secret_key}}",
  "password": "{{password}}"
}
HTTP 200
[Captures]
access_token: jsonpath "$.accessToken"

# Add Merchant Type
POST http://localhost:3000/v1/merchant-types
Content-Type: application/json
x-access-token: {{access_token}}
{
  "type": "pronet_mock",
  "url": "http://localhost:3000",
  "schema": {
    "serverUrl": {
      "type": "text",
      "title": "MERCHANT.PARAMETERS.serverUrl",
      "defaultValue": ""
    },
    "vendorCode": {
      "type": "text",
      "title": "MERCHANT.PARAMETERS.vendorCode",
      "defaultValue": ""
    },
    "genericId": {
      "type": "text",
      "title": "MERCHANT.PARAMETERS.genericId",
      "defaultValue": ""
    },
    "genericSecretKey": {
      "type": "text",
      "title": "MERCHANT.PARAMETERS.genericSecretKey",
      "defaultValue": ""
    }
  }
}

# Add Jurisdiction
POST http://localhost:3000/v1/jurisdictions
Content-Type: application/json
x-access-token: {{access_token}}
{
  "title": "CW",
  "code": "CW",
  "description": "",
  "defaultCountry": "BY",
  "settings": {
  }
}

# Add Entity
POST http://localhost:3000/v1/entities
Content-Type: application/json
x-access-token: {{access_token}}
{
  "name": "pronet_integration",
  "description": "Pronet integration",
  "title": "pronet_Integration",
  "defaultCurrency": "EUR",
  "defaultLanguage": "en",
  "languages": ["en"],
  "currencies": ["AMD", "AOA", "ARS", "AUD", "AZN", "BGN", "BMD", "BND", "BRL", "BTC", "CAD", "CDF", "CHF", "CLP", "CNY", "COP", "CRC", "CZK", "DKK", "DOP", "ETH", "EUR", "GBP", "GEL", "GHS", "GNF", "HKD", "HNL", "HRK", "HUF", "IDR", "IDS", "ILS", "INR", "ISK", "JPY", "KES", "KGS", "KRW", "KZT", "LAK", "LTC", "MAD", "MDL", "MMK", "MNT", "MOP", "MWK", "MXN", "MYR", "MZN", "NGN", "NIO", "NOK", "NZD", "PEN", "PHP", "PLN", "PYG", "RON", "RSD", "RUB", "RWF", "SEK", "SGD", "SLL", "THB", "TRY", "TWD", "TZS", "UAH", "UGX", "USD", "UYU", "VES", "VND", "VNS", "XAF", "XOF", "XXX", "ZAR", "ZMW"],
  "domain": "",
  "jurisdictionCode": "CW",
  "merchantTypes": ["pronet_mock"]
}

# Update Entity Settings
PATCH http://localhost:3000/v1/entities/pronet_integration/settings
Content-Type: application/json
x-access-token: {{access_token}}
{
  "splitPayment": true,
  "useCountriesFromJurisdiction": true,
  "urlParams": {
    "ws": 1
  }
}

# Add Merchant Entity
POST http://localhost:3000/v1/merchantentities/pronet_integration
Content-Type: application/json
x-access-token: {{access_token}}
{
  "name": "pronet_mock_1__CW",
  "title": "pronet_mock",
  "jurisdictionCode": "CW",
  "type": "pronet_mock",
  "code": "pronet_mock_1__CW",
  "defaultLanguage": "en",
  "defaultCurrency": "EUR",
  "webSiteUrl": "http://pronet.com",
  "params": {
    "serverUrl": "http://localhost:3003",
    "vendorCode": "vendor_code",
    "genericId": "generic_id",
    "genericSecretKey": "generic_secret_key"
  }
}
