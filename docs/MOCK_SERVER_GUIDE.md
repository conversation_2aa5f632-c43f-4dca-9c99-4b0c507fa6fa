# Comprehensive Mock Server Implementation Guide for TypeScript Projects

This guide provides a complete approach to implementing a robust mock server in TypeScript projects using the "@skywind-group/sw-integration-core" package, covering setup, implementation patterns, and best practices.

## Table of Contents

1. [Setup and Configuration](#setup-and-configuration)
2. [Core Implementation](#core-implementation)
3. [Data Management](#data-management)
4. [Integration Guidelines](#integration-guidelines)
5. [Advanced Features](#advanced-features)
6. [Testing and Debugging](#testing-and-debugging)

## 1. Setup and Configuration

### Dependencies Installation

Install the necessary dependencies for mock server functionality:

```bash
# Core dependencies - sw-integration-core provides the mock framework
npm install @skywind-group/sw-integration-core
npm install @nestjs/common @nestjs/core @nestjs/platform-fastify
npm install @nestjs/swagger
npm install fastify @fastify/static
npm install class-transformer class-validator

# Development dependencies
npm install --save-dev @types/node typescript ts-node
npm install --save-dev env-cmd # For environment management
```

### Project Structure

Organize your mock server files following the sw-integration-core patterns:

```
src/
├── mock/
│   ├── controllers/
│   │   └── mock.controller.ts
│   ├── services/
│   │   └── mock.service.ts
│   ├── responders/
│   │   ├── auth.responder.ts
│   │   ├── users.responder.ts
│   │   ├── orders.responder.ts
│   │   └── ping.responder.ts
│   ├── guards/
│   │   ├── auth.guard.ts
│   │   └── validation.guard.ts
│   ├── entities/
│   │   ├── auth.entities.ts
│   │   ├── user.entities.ts
│   │   └── order.entities.ts
│   ├── error.filter.ts
│   └── mock.module.ts
├── common/
│   └── entities/
│       └── api.entities.ts
├── config.ts
└── mainMock.ts
```

### Configuration Setup

Create a configuration file compatible with sw-integration-core:

```typescript
// src/config.ts
export interface Config {
  server: {
    mockPort: number;
    host: string;
  };
  internalServer: {
    port: number;
  };
  mock: {
    enableLogging: boolean;
    latency?: number;
    latencySpreading?: number;
  };
}

const config: Config = {
  server: {
    mockPort: parseInt(process.env.MOCK_PORT || '3001'),
    host: process.env.MOCK_HOST || '0.0.0.0',
  },
  internalServer: {
    port: parseInt(process.env.INTERNAL_SERVER_PORT || '4006'),
  },
  mock: {
    enableLogging: process.env.MOCK_LOGGING === 'true',
    latency: process.env.MOCK_LATENCY ? parseInt(process.env.MOCK_LATENCY) : undefined,
    latencySpreading: process.env.MOCK_LATENCY_SPREADING ? parseInt(process.env.MOCK_LATENCY_SPREADING) : undefined,
  },
};

export default config;
```

## 2. Core Implementation

### Base Mock Server Setup

Create the main mock server entry point using sw-integration-core's bootstrapMock:

```typescript
// src/mainMock.ts
import { bootstrapMock } from "@skywind-group/sw-integration-core";
import { MockModule } from "./mock/mock.module";
import config from "./config";

async function bootstrap() {
    const app = await bootstrapMock({
        serviceName: "api-mock-server",
        versionFile: "./out/version",
        module: MockModule,
        internalPort: config.internalServer.port,
        port: config.server.mockPort,
        actions: ["login", "users", "orders", "payments"], // Define your API actions
        doNotStartApplication: true
    });

    // Optional: Register additional Fastify plugins
    await app.register((fastify, options, done) => done());

    await app.listen(config.server.mockPort, config.server.host);
    console.log(`Mock server running on http://${config.server.host}:${config.server.mockPort}`);
}

bootstrap().catch(console.error);
```

### Mock Module Configuration

```typescript
// src/mock/mock.module.ts
import { MiddlewareConsumer, Module, NestModule } from "@nestjs/common";
import { mock } from "@skywind-group/sw-integration-core";
import { MockController } from "./mock.controller";
import { MockService } from "./mock.service";
import { PingResponder } from "./responders/ping.responder";
import { AuthResponder } from "./responders/auth.responder";
import { UsersResponder } from "./responders/users.responder";
import { OrdersResponder } from "./responders/orders.responder";

@Module({
    controllers: [MockController],
    providers: [
        MockService,
        PingResponder,
        AuthResponder,
        UsersResponder,
        OrdersResponder
    ],
    imports: [mock.MockModule] // Import the core mock module
})
export class MockModule implements NestModule {
    configure(consumer: MiddlewareConsumer): any {
        consumer
            .apply(PingResponder).forRoutes("/api/ping")
            .apply(AuthResponder).forRoutes("/api/auth/*")
            .apply(UsersResponder).forRoutes("/api/users", "/api/users/*")
            .apply(OrdersResponder).forRoutes("/api/orders", "/api/orders/*");
    }
}
```

### Base Responder Pattern

Use the NotSaveAnyDataMiddleware from sw-integration-core as the base for responders:

```typescript
// src/mock/responders/base.responder.ts
import { Injectable } from '@nestjs/common';
import { mock } from "@skywind-group/sw-integration-core";
import * as http from "http";

@Injectable()
export abstract class BaseResponder extends mock.NotSaveAnyDataMiddleware {
    constructor(protected settingsService: mock.SettingsService) {
        super(settingsService);
    }

    // Abstract method that must be implemented by each responder
    public abstract async getResponse(req: http.IncomingMessage, body: any): Promise<any>;

    // The middleware automatically handles:
    // - Request body parsing
    // - Response delay simulation (if configured)
    // - Error handling
    // - Content-Type headers
    // - Mock response headers
}
```

### Entity Definitions

Define your API request/response entities based on the external API you're mocking:

```typescript
// src/common/entities/auth.entities.ts
export interface LoginRequest {
    username: string;
    password: string;
}

export interface LoginResponse {
    success: boolean;
    token: string;
    user: {
        id: string;
        username: string;
        email: string;
    };
    expiresIn: number;
}

export interface VerifyTokenRequest {
    token: string;
}

export interface VerifyTokenResponse {
    valid: boolean;
    user?: {
        id: string;
        username: string;
        email: string;
    };
}
```

```typescript
// src/common/entities/user.entities.ts
export interface User {
    id: string;
    username: string;
    email: string;
    firstName: string;
    lastName: string;
    createdAt: string;
    updatedAt: string;
}

export interface CreateUserRequest {
    username: string;
    email: string;
    firstName: string;
    lastName: string;
    password: string;
}

export interface UpdateUserRequest {
    email?: string;
    firstName?: string;
    lastName?: string;
}

export interface UserResponse {
    success: boolean;
    user: User;
}

export interface UsersListResponse {
    success: boolean;
    users: User[];
    total: number;
    page: number;
    limit: number;
}
```

```typescript
// src/common/entities/order.entities.ts
export interface Order {
    id: string;
    userId: string;
    items: OrderItem[];
    total: number;
    status: 'pending' | 'confirmed' | 'shipped' | 'delivered' | 'cancelled';
    createdAt: string;
    updatedAt: string;
}

export interface OrderItem {
    productId: string;
    quantity: number;
    price: number;
}

export interface CreateOrderRequest {
    userId: string;
    items: OrderItem[];
}

export interface UpdateOrderRequest {
    status?: Order['status'];
    items?: OrderItem[];
}

export interface OrderResponse {
    success: boolean;
    order: Order;
}
```

### Specific Responder Implementations

Create specific responders for different endpoints:

```typescript
// src/mock/responders/auth.responder.ts
import { Injectable } from "@nestjs/common";
import { mock } from "@skywind-group/sw-integration-core";
import { LoginRequest, LoginResponse, VerifyTokenRequest, VerifyTokenResponse } from "../../common/entities/auth.entities";
import * as http from "http";

@Injectable()
export class AuthResponder extends mock.NotSaveAnyDataMiddleware {
    constructor(settingsService: mock.SettingsService) {
        super(settingsService);
    }

    public async getResponse(req: http.IncomingMessage, body: any): Promise<any> {
        const url = new URL(req.url!, `http://${req.headers.host}`);
        const method = req.method;

        // Route based on URL path and method
        if (url.pathname === '/api/auth/login' && method === 'POST') {
            return this.handleLogin(body as LoginRequest);
        }

        if (url.pathname === '/api/auth/verify' && method === 'POST') {
            return this.handleVerifyToken(body as VerifyTokenRequest);
        }

        throw new Error(`Unsupported endpoint: ${method} ${url.pathname}`);
    }

    private async handleLogin(body: LoginRequest): Promise<LoginResponse> {
        // Mock authentication logic
        if (body.username === 'testuser' && body.password === 'password123') {
            return {
                success: true,
                token: this.generateToken(),
                user: {
                    id: 'user-123',
                    username: body.username,
                    email: '<EMAIL>'
                },
                expiresIn: 3600
            };
        }

        throw new Error('Invalid credentials');
    }

    private async handleVerifyToken(body: VerifyTokenRequest): Promise<VerifyTokenResponse> {
        // Mock token validation
        if (body.token && body.token.startsWith('mock_token_')) {
            return {
                valid: true,
                user: {
                    id: 'user-123',
                    username: 'testuser',
                    email: '<EMAIL>'
                }
            };
        }

        return { valid: false };
    }

    private generateToken(): string {
        return `mock_token_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
}
```

```typescript
// src/mock/responders/users.responder.ts
import { Injectable } from "@nestjs/common";
import { mock } from "@skywind-group/sw-integration-core";
import { User, CreateUserRequest, UpdateUserRequest, UserResponse, UsersListResponse } from "../../common/entities/user.entities";
import * as http from "http";

@Injectable()
export class UsersResponder extends mock.NotSaveAnyDataMiddleware {
    private mockUsers: User[] = [
        {
            id: 'user-1',
            username: 'testuser',
            email: '<EMAIL>',
            firstName: 'Test',
            lastName: 'User',
            createdAt: '2023-01-01T00:00:00Z',
            updatedAt: '2023-01-01T00:00:00Z'
        }
    ];

    constructor(settingsService: mock.SettingsService) {
        super(settingsService);
    }

    public async getResponse(req: http.IncomingMessage, body: any): Promise<any> {
        const url = new URL(req.url!, `http://${req.headers.host}`);
        const method = req.method;
        const pathParts = url.pathname.split('/').filter(Boolean);

        if (url.pathname === '/api/users' && method === 'GET') {
            return this.handleGetUsers(url.searchParams);
        }

        if (url.pathname === '/api/users' && method === 'POST') {
            return this.handleCreateUser(body as CreateUserRequest);
        }

        if (pathParts.length === 3 && pathParts[0] === 'api' && pathParts[1] === 'users') {
            const userId = pathParts[2];

            if (method === 'GET') {
                return this.handleGetUser(userId);
            }

            if (method === 'PUT') {
                return this.handleUpdateUser(userId, body as UpdateUserRequest);
            }

            if (method === 'DELETE') {
                return this.handleDeleteUser(userId);
            }
        }

        throw new Error(`Unsupported endpoint: ${method} ${url.pathname}`);
    }

    private async handleGetUsers(searchParams: URLSearchParams): Promise<UsersListResponse> {
        const page = parseInt(searchParams.get('page') || '1');
        const limit = parseInt(searchParams.get('limit') || '10');
        const startIndex = (page - 1) * limit;
        const endIndex = startIndex + limit;

        return {
            success: true,
            users: this.mockUsers.slice(startIndex, endIndex),
            total: this.mockUsers.length,
            page,
            limit
        };
    }

    private async handleGetUser(userId: string): Promise<UserResponse> {
        const user = this.mockUsers.find(u => u.id === userId);
        if (!user) {
            throw new Error('User not found');
        }

        return {
            success: true,
            user
        };
    }

    private async handleCreateUser(body: CreateUserRequest): Promise<UserResponse> {
        const newUser: User = {
            id: `user-${Date.now()}`,
            username: body.username,
            email: body.email,
            firstName: body.firstName,
            lastName: body.lastName,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        this.mockUsers.push(newUser);

        return {
            success: true,
            user: newUser
        };
    }

    private async handleUpdateUser(userId: string, body: UpdateUserRequest): Promise<UserResponse> {
        const userIndex = this.mockUsers.findIndex(u => u.id === userId);
        if (userIndex === -1) {
            throw new Error('User not found');
        }

        this.mockUsers[userIndex] = {
            ...this.mockUsers[userIndex],
            ...body,
            updatedAt: new Date().toISOString()
        };

        return {
            success: true,
            user: this.mockUsers[userIndex]
        };
    }

    private async handleDeleteUser(userId: string): Promise<{ success: boolean }> {
        const userIndex = this.mockUsers.findIndex(u => u.id === userId);
        if (userIndex === -1) {
            throw new Error('User not found');
        }

        this.mockUsers.splice(userIndex, 1);

        return { success: true };
    }
}
```

```typescript
// src/mock/responders/ping.responder.ts
import { Injectable } from "@nestjs/common";
import { mock } from "@skywind-group/sw-integration-core";

@Injectable()
export class PingResponder extends mock.NotSaveAnyDataMiddleware {
    constructor(settingsService: mock.SettingsService) {
        super(settingsService);
    }

    public async getResponse(_req: any, _body: {}): Promise<any> {
        return {
            status: "OK",
            timestamp: new Date().toISOString(),
            uptime: process.uptime()
        };
    }
}
```
```

## 3. Analyzing and Adapting External APIs

### Step 1: API Analysis

Before implementing mock endpoints, analyze the external API you need to mock:

```typescript
// Example: Analyzing a REST API
// 1. Identify all endpoints and their HTTP methods
const apiEndpoints = {
  authentication: {
    'POST /api/auth/login': { request: 'LoginRequest', response: 'LoginResponse' },
    'POST /api/auth/logout': { request: 'LogoutRequest', response: 'LogoutResponse' },
    'POST /api/auth/refresh': { request: 'RefreshRequest', response: 'RefreshResponse' }
  },
  users: {
    'GET /api/users': { response: 'UsersListResponse' },
    'POST /api/users': { request: 'CreateUserRequest', response: 'UserResponse' },
    'GET /api/users/:id': { response: 'UserResponse' },
    'PUT /api/users/:id': { request: 'UpdateUserRequest', response: 'UserResponse' },
    'DELETE /api/users/:id': { response: 'DeleteResponse' }
  },
  orders: {
    'GET /api/orders': { response: 'OrdersListResponse' },
    'POST /api/orders': { request: 'CreateOrderRequest', response: 'OrderResponse' },
    'GET /api/orders/:id': { response: 'OrderResponse' },
    'PATCH /api/orders/:id/status': { request: 'UpdateOrderStatusRequest', response: 'OrderResponse' }
  }
};
```

### Step 2: Create Entity Definitions

Based on the API documentation or existing requests/responses, create TypeScript interfaces:

```typescript
// Method 1: From API Documentation
// If you have OpenAPI/Swagger documentation, extract the schemas

// Method 2: From Network Requests
// Capture actual requests/responses and create interfaces
interface CapturedLoginRequest {
  username: string;
  password: string;
  rememberMe?: boolean;
}

interface CapturedLoginResponse {
  access_token: string;
  refresh_token: string;
  expires_in: number;
  user: {
    id: number;
    email: string;
    name: string;
    roles: string[];
  };
}

// Method 3: From Existing Code
// If you have existing API client code, extract the types
```

### Step 3: Implement Responders

Create responders that match the external API's behavior:

```typescript
// src/mock/responders/external-api.responder.ts
import { Injectable } from "@nestjs/common";
import { mock } from "@skywind-group/sw-integration-core";
import * as http from "http";

@Injectable()
export class ExternalApiResponder extends mock.NotSaveAnyDataMiddleware {
    constructor(settingsService: mock.SettingsService) {
        super(settingsService);
    }

    public async getResponse(req: http.IncomingMessage, body: any): Promise<any> {
        const url = new URL(req.url!, `http://${req.headers.host}`);
        const method = req.method;

        // Create a routing system that matches the external API
        const route = `${method} ${url.pathname}`;

        switch (route) {
            case 'POST /api/auth/login':
                return this.handleLogin(body);
            case 'GET /api/users':
                return this.handleGetUsers(url.searchParams);
            case 'POST /api/users':
                return this.handleCreateUser(body);
            // Add more routes as needed
            default:
                throw new Error(`Unsupported route: ${route}`);
        }
    }

    private async handleLogin(body: any) {
        // Implement the exact response structure of the external API
        return {
            access_token: this.generateToken(),
            refresh_token: this.generateToken(),
            expires_in: 3600,
            user: {
                id: 123,
                email: body.username,
                name: "Mock User",
                roles: ["user"]
            }
        };
    }

    private generateToken(): string {
        return `mock_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
}
```

### Step 4: Configure Routes

Map your responders to match the external API's URL structure:

```typescript
// src/mock/mock.module.ts
@Module({
    // ... providers
})
export class MockModule implements NestModule {
    configure(consumer: MiddlewareConsumer): any {
        // Match the exact URL patterns of the external API
        consumer
            .apply(ExternalApiResponder)
            .forRoutes(
                "/api/auth/*",      // All auth endpoints
                "/api/users",       // Users list
                "/api/users/*",     // Individual user operations
                "/api/orders",      // Orders list
                "/api/orders/*"     // Individual order operations
            );
    }
}
```

### Step 5: Handle Different HTTP Methods

Implement proper handling for all HTTP methods used by the external API:

```typescript
public async getResponse(req: http.IncomingMessage, body: any): Promise<any> {
    const url = new URL(req.url!, `http://${req.headers.host}`);
    const method = req.method;
    const pathParts = url.pathname.split('/').filter(Boolean);

    // Handle different HTTP methods
    switch (method) {
        case 'GET':
            return this.handleGetRequest(url, pathParts);
        case 'POST':
            return this.handlePostRequest(url, pathParts, body);
        case 'PUT':
            return this.handlePutRequest(url, pathParts, body);
        case 'PATCH':
            return this.handlePatchRequest(url, pathParts, body);
        case 'DELETE':
            return this.handleDeleteRequest(url, pathParts);
        default:
            throw new Error(`Unsupported HTTP method: ${method}`);
    }
}

private async handleGetRequest(url: URL, pathParts: string[]): Promise<any> {
    if (pathParts[0] === 'api' && pathParts[1] === 'users') {
        if (pathParts.length === 2) {
            // GET /api/users - list users
            return this.getUsersList(url.searchParams);
        } else if (pathParts.length === 3) {
            // GET /api/users/:id - get specific user
            return this.getUser(pathParts[2]);
        }
    }

    throw new Error(`Unsupported GET endpoint: ${url.pathname}`);
}
```

## 4. Data Management

### Mock Service Implementation

Create a service to handle business logic and data management:

```typescript
// src/mock/mock.service.ts
import { Injectable } from "@nestjs/common";
import { mock } from "@skywind-group/sw-integration-core";

@Injectable()
export class MockService {
    constructor(
        protected readonly settingsService: mock.SettingsService
    ) {}

    // Generic data storage for different entity types
    private mockData = new Map<string, any[]>();

    // Initialize mock data
    initializeData() {
        this.mockData.set('users', [
            {
                id: 'user-1',
                username: 'testuser',
                email: '<EMAIL>',
                firstName: 'Test',
                lastName: 'User',
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            }
        ]);

        this.mockData.set('orders', [
            {
                id: 'order-1',
                userId: 'user-1',
                items: [
                    { productId: 'prod-1', quantity: 2, price: 29.99 }
                ],
                total: 59.98,
                status: 'pending',
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            }
        ]);
    }

    // Generic CRUD operations
    findAll<T>(entityType: string): T[] {
        return this.mockData.get(entityType) || [];
    }

    findById<T>(entityType: string, id: string): T | undefined {
        const entities = this.mockData.get(entityType) || [];
        return entities.find((entity: any) => entity.id === id);
    }

    create<T>(entityType: string, data: Partial<T>): T {
        const entities = this.mockData.get(entityType) || [];
        const newEntity = {
            id: this.generateId(),
            ...data,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        } as T;

        entities.push(newEntity);
        this.mockData.set(entityType, entities);
        return newEntity;
    }

    update<T>(entityType: string, id: string, data: Partial<T>): T | null {
        const entities = this.mockData.get(entityType) || [];
        const index = entities.findIndex((entity: any) => entity.id === id);

        if (index === -1) {
            return null;
        }

        entities[index] = {
            ...entities[index],
            ...data,
            updatedAt: new Date().toISOString()
        };

        this.mockData.set(entityType, entities);
        return entities[index];
    }

    delete(entityType: string, id: string): boolean {
        const entities = this.mockData.get(entityType) || [];
        const index = entities.findIndex((entity: any) => entity.id === id);

        if (index === -1) {
            return false;
        }

        entities.splice(index, 1);
        this.mockData.set(entityType, entities);
        return true;
    }

    // Utility methods
    private generateId(): string {
        return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }

    // Pagination helper
    paginate<T>(entities: T[], page: number = 1, limit: number = 10): {
        data: T[];
        total: number;
        page: number;
        limit: number;
        totalPages: number;
    } {
        const startIndex = (page - 1) * limit;
        const endIndex = startIndex + limit;
        const data = entities.slice(startIndex, endIndex);

        return {
            data,
            total: entities.length,
            page,
            limit,
            totalPages: Math.ceil(entities.length / limit)
        };
    }
}
```

### Using Built-in Services

The sw-integration-core provides several built-in services for data management:

```typescript
// Available services from mock.* namespace:

// CustomerService - manages customer data
interface Customer {
    cust_id: string;
    cust_session_id: string;
    currency_code: string;
    balance: { amount: number };
    country: string;
    test_cust: boolean;
}

// SessionService - manages session data
interface Session {
    id: string;
    merchantId: string;
    customerId: string;
    active: boolean;
}

// TicketService - manages authentication tickets
// Methods: getDataFromTicket(ticketId: string): [customerId: string, currencyCode: string]

// TransactionService - manages transaction data
interface Transaction {
    id: string;
    externalId: string;
    amount: number;
    type: 'credit' | 'debit' | 'rollback';
    status: string;
}

// SettingsService - manages mock settings
interface Settings {
    amount: number;
    contentType: string;
    // ... other settings
}
```
```

### Guards Implementation

Implement guards for authentication and validation:

```typescript
// src/mock/guards/auth.guard.ts
import { Injectable, CanActivate, ExecutionContext } from "@nestjs/common";
import { mock } from "@skywind-group/sw-integration-core";

@Injectable()
export class AuthGuard implements CanActivate {
    canActivate(context: ExecutionContext): boolean {
        const request = context.switchToHttp().getRequest();
        const authHeader = request.headers.authorization;

        // Mock token validation
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            throw new Error('Missing or invalid authorization header');
        }

        const token = authHeader.substring(7);

        // Simple mock validation - in real implementation, validate against your auth system
        if (!token.startsWith('mock_token_')) {
            throw new Error('Invalid token format');
        }

        // Add user info to request for use in responders
        request.user = {
            id: 'user-123',
            username: 'testuser',
            email: '<EMAIL>'
        };

        return true;
    }
}
```

```typescript
// src/mock/guards/validation.guard.ts
import { Injectable, CanActivate, ExecutionContext, BadRequestException } from "@nestjs/common";
import { validate } from "class-validator";
import { plainToClass } from "class-transformer";

@Injectable()
export class ValidationGuard implements CanActivate {
    constructor(private readonly dtoClass: any) {}

    async canActivate(context: ExecutionContext): Promise<boolean> {
        const request = context.switchToHttp().getRequest();

        if (!request.body) {
            throw new BadRequestException('Request body is required');
        }

        // If you're using class-validator, validate the request body
        if (this.dtoClass) {
            const dto = plainToClass(this.dtoClass, request.body);
            const errors = await validate(dto);

            if (errors.length > 0) {
                const errorMessages = errors.map(error =>
                    Object.values(error.constraints || {}).join(', ')
                ).join('; ');

                throw new BadRequestException(`Validation failed: ${errorMessages}`);
            }

            request.validatedBody = dto;
        }

        return true;
    }
}
```

## 5. Integration Guidelines

### Mock Controller Implementation

Create a controller that demonstrates various API patterns:

```typescript
// src/mock/mock.controller.ts
import { Body, Controller, Get, Post, Put, Delete, Param, Query, UseFilters, UseGuards, UseInterceptors } from "@nestjs/common";
import { mock } from "@skywind-group/sw-integration-core";
import { MockService } from "./mock.service";
import { AuthGuard } from "./guards/auth.guard";
import { ValidationGuard } from "./guards/validation.guard";
import { ErrorFilter } from "./error.filter";
import {
    LoginRequest,
    CreateUserRequest,
    UpdateUserRequest,
    CreateOrderRequest
} from "../common/entities/api.entities";

@Controller("/api")
@UseInterceptors(mock.CustomErrorInterceptor)
@UseFilters(ErrorFilter)
export class MockController {
    constructor(private readonly service: MockService) {
        // Initialize mock data
        this.service.initializeData();
    }

    // Health check endpoint
    @Get("ping")
    public ping() {
        return {
            status: "OK",
            timestamp: new Date().toISOString(),
            uptime: process.uptime()
        };
    }

    // Authentication endpoints
    @Post("auth/login")
    public login(@Body() body: LoginRequest) {
        // Mock authentication logic
        if (body.username === 'testuser' && body.password === 'password123') {
            return {
                success: true,
                token: `mock_token_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                user: {
                    id: 'user-123',
                    username: body.username,
                    email: '<EMAIL>'
                },
                expiresIn: 3600
            };
        }
        throw new Error('Invalid credentials');
    }

    // User management endpoints
    @Get("users")
    @UseGuards(AuthGuard)
    public getUsers(@Query('page') page: string = '1', @Query('limit') limit: string = '10') {
        const users = this.service.findAll('users');
        const pageNum = parseInt(page);
        const limitNum = parseInt(limit);
        const result = this.service.paginate(users, pageNum, limitNum);

        return {
            success: true,
            ...result
        };
    }

    @Get("users/:id")
    @UseGuards(AuthGuard)
    public getUser(@Param('id') id: string) {
        const user = this.service.findById('users', id);
        if (!user) {
            throw new Error('User not found');
        }

        return {
            success: true,
            user
        };
    }

    @Post("users")
    @UseGuards(AuthGuard)
    public createUser(@Body() body: CreateUserRequest) {
        const user = this.service.create('users', body);

        return {
            success: true,
            user
        };
    }

    @Put("users/:id")
    @UseGuards(AuthGuard)
    public updateUser(@Param('id') id: string, @Body() body: UpdateUserRequest) {
        const user = this.service.update('users', id, body);
        if (!user) {
            throw new Error('User not found');
        }

        return {
            success: true,
            user
        };
    }

    @Delete("users/:id")
    @UseGuards(AuthGuard)
    public deleteUser(@Param('id') id: string) {
        const deleted = this.service.delete('users', id);
        if (!deleted) {
            throw new Error('User not found');
        }

        return {
            success: true
        };
    }

    // Order management endpoints
    @Get("orders")
    @UseGuards(AuthGuard)
    public getOrders(@Query('page') page: string = '1', @Query('limit') limit: string = '10') {
        const orders = this.service.findAll('orders');
        const pageNum = parseInt(page);
        const limitNum = parseInt(limit);
        const result = this.service.paginate(orders, pageNum, limitNum);

        return {
            success: true,
            ...result
        };
    }

    @Post("orders")
    @UseGuards(AuthGuard)
    public createOrder(@Body() body: CreateOrderRequest) {
        const order = this.service.create('orders', {
            ...body,
            total: body.items.reduce((sum, item) => sum + (item.price * item.quantity), 0),
            status: 'pending'
        });

        return {
            success: true,
            order
        };
    }
}
```

### Error Filter Implementation

Create an error filter to handle mock-specific errors:

```typescript
// src/mock/error.filter.ts
import { ArgumentsHost, Catch, ExceptionFilter, HttpStatus } from "@nestjs/common";
import { mock } from "@skywind-group/sw-integration-core";

@Catch()
export class ErrorFilter implements ExceptionFilter {
    catch(exception: any, host: ArgumentsHost): any {
        const ctx = host.switchToHttp();
        const response = ctx.getResponse();

        // Handle specific mock errors
        if (exception instanceof mock.errors.GameTokenExpired) {
            return response.status(HttpStatus.UNAUTHORIZED).json({
                status: "ERROR",
                errorCode: "GAME_TOKEN_EXPIRED",
                message: exception.message
            });
        }

        if (exception instanceof mock.errors.InsufficientBalance) {
            return response.status(HttpStatus.BAD_REQUEST).json({
                status: "ERROR",
                errorCode: "INSUFFICIENT_BALANCE",
                message: exception.message
            });
        }

        if (exception instanceof mock.errors.TransactionNotFound) {
            return response.status(HttpStatus.NOT_FOUND).json({
                status: "ERROR",
                errorCode: "TRANSACTION_NOT_FOUND",
                message: exception.message
            });
        }

        // Default error handling
        return response.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
            status: "ERROR",
            errorCode: "INTERNAL_ERROR",
            message: "An unexpected error occurred"
        });
    }
}
```

### Environment Configuration

Configure different environments for mock server usage:

```typescript
// Environment variables for mock server
export const mockEnvironmentConfig = {
  development: {
    MOCK_PORT: '3001',
    INTERNAL_SERVER_PORT: '4006',
    MOCK_LOGGING: 'true',
    MOCK_LATENCY: '100',
    MOCK_LATENCY_SPREADING: '50',
    NODE_ENV: 'development'
  },
  testing: {
    MOCK_PORT: '3002',
    INTERNAL_SERVER_PORT: '4007',
    MOCK_LOGGING: 'false',
    MOCK_LATENCY: '0',
    NODE_ENV: 'testing'
  },
  production: {
    // Mock server typically not used in production
    MOCK_ENABLED: 'false'
  }
};
```
```

### Docker Configuration for Mock Server

```dockerfile
# Dockerfile.mock
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

EXPOSE 3001

CMD ["npm", "run", "start:mock"]
```

```yaml
# docker-compose.mock.yml
version: '3.8'
services:
  mock-server:
    build:
      context: .
      dockerfile: Dockerfile.mock
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=development
      - MOCK_PORT=3001
      - MOCK_LOGGING=true
      - USE_MOCK=true
    volumes:
      - ./src/mock/data:/app/src/mock/data
```

## 6. API Adaptation Guidelines

### Identifying Endpoints to Mock

When adapting this guide to mock a specific external API, follow these steps:

#### Step 1: API Discovery
```typescript
// Document all endpoints from the target API
interface ApiEndpointMap {
  [category: string]: {
    [endpoint: string]: {
      method: string;
      path: string;
      requestType?: string;
      responseType: string;
      authRequired: boolean;
      description: string;
    };
  };
}

// Example for a typical REST API
const targetApiEndpoints: ApiEndpointMap = {
  authentication: {
    login: {
      method: 'POST',
      path: '/auth/login',
      requestType: 'LoginRequest',
      responseType: 'LoginResponse',
      authRequired: false,
      description: 'User authentication'
    },
    refresh: {
      method: 'POST',
      path: '/auth/refresh',
      requestType: 'RefreshRequest',
      responseType: 'TokenResponse',
      authRequired: true,
      description: 'Refresh access token'
    }
  },
  users: {
    list: {
      method: 'GET',
      path: '/users',
      responseType: 'UserListResponse',
      authRequired: true,
      description: 'Get paginated user list'
    },
    create: {
      method: 'POST',
      path: '/users',
      requestType: 'CreateUserRequest',
      responseType: 'UserResponse',
      authRequired: true,
      description: 'Create new user'
    }
  }
};
```

#### Step 2: Request/Response Analysis
```typescript
// Capture actual API requests and responses to create accurate types
// Method 1: From API documentation
interface ExternalApiUser {
  id: number;
  email: string;
  first_name: string;
  last_name: string;
  avatar?: string;
  created_at: string;
  updated_at: string;
}

// Method 2: From network inspection
interface CapturedResponse {
  data: ExternalApiUser[];
  page: number;
  per_page: number;
  total: number;
  total_pages: number;
}

// Method 3: From existing client code
// Extract types from your existing API client if available
```

#### Step 3: Create Matching Entity Definitions
```typescript
// src/entities/external-api.entities.ts
// Mirror the exact structure of the external API

export interface ExternalLoginRequest {
  email: string;
  password: string;
}

export interface ExternalLoginResponse {
  token: string;
  user: {
    id: number;
    email: string;
    first_name: string;
    last_name: string;
  };
}

export interface ExternalUserListResponse {
  page: number;
  per_page: number;
  total: number;
  total_pages: number;
  data: ExternalApiUser[];
}
```

#### Step 4: Implement Custom Responders
```typescript
// src/mock/responders/external-api.responder.ts
@Injectable()
export class ExternalApiResponder extends mock.NotSaveAnyDataMiddleware {
  constructor(settingsService: mock.SettingsService) {
    super(settingsService);
  }

  public async getResponse(req: http.IncomingMessage, body: any): Promise<any> {
    const url = new URL(req.url!, `http://${req.headers.host}`);
    const method = req.method;

    // Create exact URL matching for the external API
    const routeKey = `${method} ${url.pathname}`;

    switch (routeKey) {
      case 'POST /auth/login':
        return this.handleExternalLogin(body);
      case 'GET /users':
        return this.handleExternalUserList(url.searchParams);
      case 'POST /users':
        return this.handleExternalCreateUser(body);
      default:
        // Return a generic error response matching the external API format
        throw new Error(`Endpoint not implemented: ${routeKey}`);
    }
  }

  private async handleExternalLogin(body: ExternalLoginRequest): Promise<ExternalLoginResponse> {
    // Implement the exact response format of the external API
    if (body.email === '<EMAIL>' && body.password === 'password') {
      return {
        token: `external_token_${Date.now()}`,
        user: {
          id: 1,
          email: body.email,
          first_name: 'Test',
          last_name: 'User'
        }
      };
    }

    // Match the external API's error format
    throw new Error('Invalid credentials');
  }

  private async handleExternalUserList(searchParams: URLSearchParams): Promise<ExternalUserListResponse> {
    const page = parseInt(searchParams.get('page') || '1');
    const perPage = parseInt(searchParams.get('per_page') || '6');

    // Mock data that matches the external API structure
    const mockUsers: ExternalApiUser[] = [
      {
        id: 1,
        email: '<EMAIL>',
        first_name: 'George',
        last_name: 'Bluth',
        avatar: 'https://reqres.in/img/faces/1-image.jpg',
        created_at: '2023-01-01T00:00:00.000Z',
        updated_at: '2023-01-01T00:00:00.000Z'
      }
      // Add more mock users as needed
    ];

    // Implement pagination exactly like the external API
    const startIndex = (page - 1) * perPage;
    const endIndex = startIndex + perPage;
    const paginatedUsers = mockUsers.slice(startIndex, endIndex);

    return {
      page,
      per_page: perPage,
      total: mockUsers.length,
      total_pages: Math.ceil(mockUsers.length / perPage),
      data: paginatedUsers
    };
  }
}
```

### Configuration for Different APIs

#### E-commerce API Example
```typescript
// Configure for an e-commerce API
const ecommerceRoutes = {
  products: '/api/v1/products',
  orders: '/api/v1/orders',
  customers: '/api/v1/customers',
  payments: '/api/v1/payments'
};

// Module configuration
consumer
  .apply(ProductsResponder).forRoutes('/api/v1/products', '/api/v1/products/*')
  .apply(OrdersResponder).forRoutes('/api/v1/orders', '/api/v1/orders/*')
  .apply(PaymentsResponder).forRoutes('/api/v1/payments/*');
```

#### Social Media API Example
```typescript
// Configure for a social media API
const socialMediaRoutes = {
  posts: '/api/posts',
  users: '/api/users',
  comments: '/api/comments',
  likes: '/api/likes'
};

// Module configuration
consumer
  .apply(PostsResponder).forRoutes('/api/posts', '/api/posts/*')
  .apply(UsersResponder).forRoutes('/api/users', '/api/users/*')
  .apply(CommentsResponder).forRoutes('/api/comments', '/api/comments/*');
```

#### Financial API Example
```typescript
// Configure for a financial/banking API
const financialRoutes = {
  accounts: '/api/v2/accounts',
  transactions: '/api/v2/transactions',
  transfers: '/api/v2/transfers',
  balances: '/api/v2/balances'
};

// Module configuration with authentication
consumer
  .apply(AuthMiddleware).forRoutes('/api/v2/*')
  .apply(AccountsResponder).forRoutes('/api/v2/accounts', '/api/v2/accounts/*')
  .apply(TransactionsResponder).forRoutes('/api/v2/transactions', '/api/v2/transactions/*');
```

## 7. Advanced Features

### Request Validation and Response Formatting

```typescript
// src/mock/guards/validation.guard.ts
import { Injectable, CanActivate, ExecutionContext, BadRequestException } from '@nestjs/common';
import { validate } from 'class-validator';
import { plainToClass } from 'class-transformer';

@Injectable()
export class ValidationGuard implements CanActivate {
  constructor(private readonly dtoClass: any) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const dto = plainToClass(this.dtoClass, request.body);

    const errors = await validate(dto);

    if (errors.length > 0) {
      const errorMessages = errors.map(error =>
        Object.values(error.constraints || {}).join(', ')
      ).join('; ');

      throw new BadRequestException(`Validation failed: ${errorMessages}`);
    }

    request.validatedBody = dto;
    return true;
  }
}
```

### Logging and Debugging Interceptor

```typescript
// src/mock/interceptors/logging.interceptor.ts
import { Injectable, NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const { method, url, body, headers } = request;

    console.log(`[MOCK] ${method} ${url}`);
    console.log(`[MOCK] Headers:`, this.sanitizeHeaders(headers));
    console.log(`[MOCK] Body:`, body);

    const startTime = Date.now();

    return next.handle().pipe(
      tap(response => {
        const duration = Date.now() - startTime;
        console.log(`[MOCK] Response (${duration}ms):`, response);
      })
    );
  }

  private sanitizeHeaders(headers: any): any {
    const sanitized = { ...headers };
    delete sanitized.authorization;
    delete sanitized.cookie;
    return sanitized;
  }
}
```

### Error Simulation with Scenarios

```typescript
// src/mock/services/error-simulation.service.ts
import { Injectable } from '@nestjs/common';

export interface ErrorScenario {
  name: string;
  condition: (req: any) => boolean;
  response: {
    status: number;
    body: any;
  };
  probability: number;
}

@Injectable()
export class ErrorSimulationService {
  private scenarios: ErrorScenario[] = [
    {
      name: 'network_timeout',
      condition: (req) => req.headers['x-simulate-timeout'] === 'true',
      response: {
        status: 408,
        body: { error: 'Request Timeout', code: 'TIMEOUT' }
      },
      probability: 1.0
    },
    {
      name: 'rate_limit',
      condition: (req) => this.isRateLimited(req),
      response: {
        status: 429,
        body: { error: 'Too Many Requests', code: 'RATE_LIMIT' }
      },
      probability: 0.1
    },
    {
      name: 'server_error',
      condition: () => true,
      response: {
        status: 500,
        body: { error: 'Internal Server Error', code: 'SERVER_ERROR' }
      },
      probability: 0.05
    }
  ];

  shouldSimulateError(req: any): ErrorScenario | null {
    for (const scenario of this.scenarios) {
      if (scenario.condition(req) && Math.random() < scenario.probability) {
        return scenario;
      }
    }
    return null;
  }

  private isRateLimited(req: any): boolean {
    // Simple rate limiting simulation based on IP
    const ip = req.ip || req.connection.remoteAddress;
    // Implementation would track requests per IP
    return false;
  }
}
```

### Dynamic Response Templates

```typescript
// src/mock/services/template.service.ts
import { Injectable } from '@nestjs/common';

@Injectable()
export class TemplateService {
  processTemplate(template: any, context: any): any {
    if (typeof template === 'string') {
      return this.processStringTemplate(template, context);
    }

    if (Array.isArray(template)) {
      return template.map(item => this.processTemplate(item, context));
    }

    if (typeof template === 'object' && template !== null) {
      const result: any = {};
      for (const [key, value] of Object.entries(template)) {
        result[key] = this.processTemplate(value, context);
      }
      return result;
    }

    return template;
  }

  private processStringTemplate(template: string, context: any): any {
    // Handle function calls like {{generateId}}
    const functionPattern = /\{\{(\w+)\}\}/g;
    let result = template.replace(functionPattern, (match, funcName) => {
      return this.callFunction(funcName, context);
    });

    // Handle property access like {{request.amount}}
    const propertyPattern = /\{\{([\w.]+)\}\}/g;
    result = result.replace(propertyPattern, (match, path) => {
      return this.getNestedProperty(context, path) || match;
    });

    // Try to parse as JSON if it looks like a JSON string
    try {
      return JSON.parse(result);
    } catch {
      return result;
    }
  }

  private callFunction(funcName: string, context: any): string {
    const functions: Record<string, () => string> = {
      generateId: () => `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      generateToken: () => `token_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: () => new Date().toISOString(),
      uuid: () => require('crypto').randomUUID(),
    };

    return functions[funcName]?.() || `{{${funcName}}}`;
  }

  private getNestedProperty(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }
}
```

## 6. Testing and Debugging

### Unit Tests for Mock Server

```typescript
// src/mock/mock.service.spec.ts
import { Test, TestingModule } from '@nestjs/testing';
import { MockService } from './services/mock.service';
import { DataService } from './services/data.service';

describe('MockService', () => {
  let service: MockService;
  let dataService: DataService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MockService,
        {
          provide: DataService,
          useValue: {
            findUser: jest.fn(),
            createTransaction: jest.fn(),
            getResponseTemplate: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<MockService>(MockService);
    dataService = module.get<DataService>(DataService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should authenticate user with valid credentials', async () => {
    const mockUser = {
      id: 'user-1',
      username: 'testuser',
      password: 'password123',
    };

    jest.spyOn(dataService, 'findUser').mockResolvedValue(mockUser);

    const result = await service.authenticateUser('testuser', 'password123');

    expect(result).toHaveProperty('token');
    expect(result.user.id).toBe('user-1');
  });
});
```

### Integration Tests

```typescript
// src/mock/integration/mock-server.integration.spec.ts
import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { MockModule } from '../mock.module';

describe('Mock Server Integration', () => {
  let app: INestApplication;

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [MockModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  afterEach(async () => {
    await app.close();
  });

  it('/api/auth/login (POST)', () => {
    return request(app.getHttpServer())
      .post('/api/auth/login')
      .send({ username: 'testuser', password: 'password123' })
      .expect(200)
      .expect(res => {
        expect(res.body).toHaveProperty('token');
        expect(res.body.success).toBe(true);
      });
  });

  it('should handle invalid credentials', () => {
    return request(app.getHttpServer())
      .post('/api/auth/login')
      .send({ username: 'invalid', password: 'wrong' })
      .expect(401);
  });
});
```

### Debugging Tools

```typescript
// src/mock/tools/debug.middleware.ts
import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';

@Injectable()
export class DebugMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: NextFunction) {
    if (process.env.NODE_ENV === 'development') {
      // Add debug headers to response
      res.setHeader('X-Mock-Server', 'true');
      res.setHeader('X-Mock-Timestamp', new Date().toISOString());
      res.setHeader('X-Mock-Request-Id', this.generateRequestId());

      // Log request details
      console.log('=== MOCK SERVER DEBUG ===');
      console.log('Method:', req.method);
      console.log('URL:', req.url);
      console.log('Headers:', req.headers);
      console.log('Body:', req.body);
      console.log('========================');
    }

    next();
  }

  private generateRequestId(): string {
    return Math.random().toString(36).substr(2, 9);
  }
}
```

## Best Practices and Patterns

### 1. Modular Design
- Separate concerns into different modules (auth, payments, users)
- Use dependency injection for better testability
- Create reusable base classes for common functionality

### 2. Configuration Management
- Use environment variables for configuration
- Support different environments (dev, test, staging)
- Allow runtime configuration changes

### 3. Data Management
- Use JSON files for simple data storage
- Implement caching for better performance
- Support data persistence between server restarts

### 4. Error Handling
- Implement realistic error scenarios
- Use proper HTTP status codes
- Provide meaningful error messages

### 5. Testing Strategy
- Write unit tests for individual components
- Create integration tests for full workflows
- Use the mock server in your application tests

### 6. Performance Considerations
- Implement response delays to simulate real network conditions
- Use caching for frequently accessed data
- Monitor memory usage for long-running instances

## Usage Examples

### Starting the Mock Server

```bash
# Development mode with hot reload
npm run start:mock:dev

# Production mode
npm run start:mock

# With custom configuration
MOCK_PORT=4000 MOCK_LOGGING=true npm run start:mock

# Using Docker
docker-compose -f docker-compose.mock.yml up
```

### Using in Tests

```typescript
// test/setup.ts
import { ApiClient } from '../src/services/api.client';

// Configure API client to use mock server
process.env.USE_MOCK = 'true';
process.env.NODE_ENV = 'testing';

const apiClient = new ApiClient();

export { apiClient };
```

### Switching Between Mock and Real API

```typescript
// In your application
const apiClient = new ApiClient();

// Use mock for development
if (process.env.NODE_ENV === 'development') {
  apiClient.setMockMode(true);
}

// Use real API for production
if (process.env.NODE_ENV === 'production') {
  apiClient.setMockMode(false);
}
```

### Custom Mock Scenarios

```typescript
// Create custom scenarios for specific test cases
const mockScenarios = {
  userNotFound: {
    endpoint: '/api/users/:id',
    method: 'GET',
    response: { status: 404, body: { error: 'User not found' } }
  },

  paymentFailure: {
    endpoint: '/api/payments/process',
    method: 'POST',
    response: { status: 400, body: { error: 'Insufficient funds' } }
  }
};
```

## Conclusion

This comprehensive guide provides a robust foundation for implementing mock servers in TypeScript projects. The patterns and practices outlined here ensure:

- **Maintainability**: Clean, modular code structure
- **Flexibility**: Easy configuration and customization
- **Reliability**: Proper error handling and testing
- **Reusability**: Generic patterns applicable to any project
- **Scalability**: Architecture that grows with your needs

By following these guidelines, you can create mock servers that significantly improve your development workflow, enable better testing practices, and provide a reliable foundation for API development and integration testing.
```
```
```
